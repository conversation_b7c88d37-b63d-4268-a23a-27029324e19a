import Phaser from 'phaser';

export class PreloadScene extends Phaser.Scene {
  private loadingBar!: Phaser.GameObjects.Graphics;
  private progressBar!: Phaser.GameObjects.Graphics;

  constructor() {
    super({ key: 'PreloadScene' });
  }

  preload() {
    this.createLoadingBar();
    this.loadAssets();
    this.setupLoadingEvents();
  }

  private createLoadingBar() {
    const width = this.cameras.main.width;
    const height = this.cameras.main.height;

    // Loading bar background
    this.loadingBar = this.add.graphics();
    this.loadingBar.fillStyle(0x222222);
    this.loadingBar.fillRect(width / 2 - 160, height / 2 - 25, 320, 50);

    // Progress bar
    this.progressBar = this.add.graphics();

    // Loading text
    this.add.text(width / 2, height / 2 - 60, 'Đang tải...', {
      fontSize: '24px',
      color: '#ffffff'
    }).setOrigin(0.5);
  }

  private loadAssets() {
    // Load sprite atlas
    this.load.atlas('game-atlas', 'assets/sprites/game-atlas.webp', 'assets/sprites/game-atlas.json');
    
    // Load audio với fallback
    // this.load.audio('click', ['assets/audio/click.ogg', 'assets/audio/click.mp3']);
    // this.load.audio('win', ['assets/audio/win.ogg', 'assets/audio/win.mp3']);
    // this.load.audio('place', ['assets/audio/place.ogg', 'assets/audio/place.mp3']);
    // this.load.audio('bgm', ['assets/audio/background.ogg', 'assets/audio/background.mp3']);

    // // Load fonts
    // this.load.bitmapFont('pixel-font', 'assets/fonts/pixel-font.png', 'assets/fonts/pixel-font.xml');
  }

  private setupLoadingEvents() {
    this.load.on('progress', (value: number) => {
      this.progressBar.clear();
      this.progressBar.fillStyle(0x00ff00);
      this.progressBar.fillRect(
        this.cameras.main.width / 2 - 150,
        this.cameras.main.height / 2 - 15,
        300 * value,
        30
      );
    });

    this.load.on('complete', () => {
      this.progressBar.destroy();
      this.loadingBar.destroy();
      this.scene.start('MenuScene');
    });
  }
}
